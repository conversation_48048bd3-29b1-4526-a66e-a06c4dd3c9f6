import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { z } from 'zod'
import { signInWithEmailAndPassword } from 'firebase/auth'
import { auth } from '../config/firebase.config'
import { fetchUser } from '../utils/app.utils'
import { toast } from 'sonner'
import { Loader2, Lock, Mail, Eye, EyeOff } from 'lucide-react'

const signInSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'Email is required' })
    .email({ message: 'Please enter a valid email address' }),
  password: z
    .string()
    .min(8, { message: 'Password must be at least 8 characters' })
    .max(100, { message: 'Password is too long' }),
})

export default function SignInPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({ email: '', password: '' })
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({})
  const navigate = useNavigate()

  const handleInputChange = (field: 'email' | 'password', value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const validateForm = (): boolean => {
    const result = signInSchema.safeParse(formData)
    if (!result.success) {
      const newErrors: { email?: string; password?: string } = {}
      result.error.errors.forEach(error => {
        if (error.path[0] === 'email' || error.path[0] === 'password') {
          newErrors[error.path[0] as 'email' | 'password'] = error.message
        }
      })
      setErrors(newErrors)
      return false
    }
    setErrors({})
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsLoading(true)

    try {
      const response = await signInWithEmailAndPassword(auth, formData.email, formData.password)
      const userData = await fetchUser(response?.user?.uid)

      // USER IS ADMIN
      if (userData && userData?.userType === 'admin') {
        localStorage.setItem('userId', response?.user?.uid)
        localStorage.setItem('userData', JSON.stringify(userData))
        toast.success('Welcome back!')
        navigate('/dashboard')
      } else {
        toast.error("Access denied. Admin privileges required.")
      }
    } catch (error: any) {
      console.error(error)

      // Handle different error types
      if (error.code === 'auth/user-not-found') {
        toast.error('No account found with this email.')
      } else if (error.code === 'auth/wrong-password' || error.code === 'auth/invalid-login-credentials') {
        toast.error('Invalid email or password.')
      } else if (error.code === 'auth/too-many-requests') {
        toast.error('Too many failed attempts. Please try again later.')
      } else {
        toast.error('Something went wrong. Please try again.')
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4 sm:p-6 lg:p-8">
      <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg xl:max-w-xl mx-auto">
        {/* Header */}
        <div className="text-center mb-6 sm:mb-8 lg:mb-10">
          
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-slate-900 mb-2 lg:mb-3">
            Akhdar Admin
          </h1>
          <p className="text-sm sm:text-base lg:text-lg text-slate-600">
            Sign in to access your dashboard
          </p>
        </div>

        {/* Form Container */}
        <div className="bg-white rounded-2xl shadow-xl border border-slate-200 p-6 sm:p-8 lg:p-10 xl:p-12 min-h-[300px] sm:min-h-[350px] lg:min-h-[400px]">
          <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-4 lg:space-y-6 h-full flex flex-col justify-center">
            {/* Email Field */}
            <div className="space-y-2 lg:space-y-3">
              <label htmlFor="email" className="flex text-sm sm:text-base lg:text-lg font-medium text-slate-700"> 
                Email Address
              </label>
              <div className="relative">
                <Mail className="absolute left-3 lg:left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 sm:w-5 sm:h-5 lg:w-4 lg:h-4 text-slate-400" />
                <input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="Enter your email"
                  className={`w-full pl-9 sm:pl-10 lg:pl-12 pr-4 py-3 sm:py-4 lg:py-3 text-sm sm:text-base lg:text-lg border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                    errors.email
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                      : 'border-slate-300'
                  }`}
                  disabled={isLoading}
                />
              </div>
              {errors.email && (
                <p className="text-xs sm:text-sm lg:text-base text-red-600 flex items-center gap-1">
                  <span className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 text-red-500">⚠</span>
                  {errors.email}
                </p>
              )}
            </div>

            {/* Password Field */}
            <div className="space-y-2 lg:space-y-3">
              <label htmlFor="password" className="flex text-sm sm:text-base lg:text-lg font-medium text-slate-700">
                Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 lg:left-4 top-1/2 transform -translate-y-1/2 w-3 h-3 sm:w-5 sm:h-5 lg:w-4 lg:h-4 text-slate-400" />
                <input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  placeholder="Enter your password"
                  className={`w-full pl-9 sm:pl-10 lg:pl-12 pr-10 sm:pr-12 lg:pr-14 py-3 sm:py-4 lg:py-3 text-sm sm:text-base lg:text-lg border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                    errors.password
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                      : 'border-slate-300'
                  }`}
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 lg:right-4 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors"
                  disabled={isLoading}
                >
                  {showPassword ? <EyeOff className="w-3 h-3 sm:w-5 sm:h-5 lg:w-4 lg:h-4" /> : <Eye className="w-4 h-4 sm:w-5 sm:h-5 lg:w-4 lg:h-4" />}
                </button>
              </div>
              {errors.password && (
                <p className="text-xs sm:text-sm lg:text-base text-red-600 flex items-center gap-1">
                  <span className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-4 text-red-500">⚠</span>
                  {errors.password}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-[#bf935e] hover:bg-opacity-80 cursor-pointer disabled:bg-[#bf935e] text-white font-medium py-3 sm:py-4 lg:py-3 px-4 lg:px-6 text-sm sm:text-base lg:text-lg rounded-xl transition-colors focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed flex items-center justify-center gap-2 lg:gap-3"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-3 h-3 sm:w-5 sm:h-5 lg:w-5 lg:h-5 animate-spin" />
                  <span className="text-sm sm:text-base lg:text-lg">Signing in...</span>
                </>
              ) : (
                <span className="text-sm sm:text-base lg:text-lg">Sign In</span>
              )}
            </button>
          </form>
        </div>

        {/* Footer */}
        <div className="text-center mt-4 sm:mt-6">
          <p className="text-xs sm:text-sm text-slate-500 px-4">
            Secure admin access for Akhdar platform
          </p>
        </div>
      </div>
    </div>
  )
}