import { useState } from 'react'
import { Edit, Trash2, Mail, Calendar } from 'lucide-react'
import { toast } from 'sonner'
import type { User } from '../types/user'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { deleteUser } from '../store/slices/usersSlice'
import EditUserModal from './EditUserModal'
import ConfirmationModal from './ConfirmationModal'
import { formatDate } from '../utils/app.utils'
import { Eye } from 'lucide-react'
import UserDetailsModal from './UserDetailsModal'

interface UserTableProps {
  users: User[]
  userType: 'manager' | 'staff' | 'client'
}

const UserTable = ({ users, userType }: UserTableProps) => {
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [userToDelete, setUserToDelete] = useState<string | null>(null)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [userToEdit, setUserToEdit] = useState<User | null>(null)
  const [detailsModalOpen, setDetailsModalOpen] = useState(false)
  const [userToView, setUserToView] = useState<User | null>(null)
  const dispatch = useAppDispatch()
  const { deleteLoading } = useAppSelector((state:any) => state.users)

  const isDeleting = deleteLoading === userToDelete

  const handleDeleteClick = (userId: string) => {
    setUserToDelete(userId)
    setDeleteModalOpen(true)
  }

  const handleEditClick = (user: User) => {
    setUserToEdit(user)
    setEditModalOpen(true)
  }

  const handleViewDetailsClick = (user: User) => {
    setUserToView(user)
    setDetailsModalOpen(true)
  }

  const confirmDelete = async () => {
    if (userToDelete) {
      try {
        await dispatch(deleteUser(userToDelete)).unwrap()
        toast.success('User deleted successfully')
        setDeleteModalOpen(false)
        setUserToDelete(null)
      } catch (error) {
        toast.error('Failed to delete user')
      }
    }
  }

  const handleCloseEditModal = () => {
    setEditModalOpen(false)
    setUserToEdit(null)
  }

  const handleCloseDeleteModal = () => {
    setDeleteModalOpen(false)
    setUserToDelete(null)
  }

  const handleCloseDetailsModal = () => {
    setDetailsModalOpen(false)
    setUserToView(null)
  }

  const getUserTypeColor = (type: string) => {
    switch (type) {
      case 'manager':
        return 'bg-blue-100 text-blue-800'
      case 'staff':
        return 'bg-green-100 text-green-800'
      case 'client':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (users.length === 0) {
    return (
      <div className="text-center py-16 bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center">
          <Mail className="w-8 h-8 text-blue-500" />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-3">
          No {userType}s found
        </h3>
        <p className="text-gray-600 max-w-md mx-auto">
          There are no {userType}s in the system yet. Start by adding your first {userType} to get started.
        </p>
      </div>
    )
  }

  return (
    <>
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  #
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  User Information
                </th>
                <th className=" py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider sm:pl-9 pl-9 lg:pl-13">
                  Role
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider whitespace-nowrap">
                  Date Created
                </th>
                <th className="px-6 py-4 text-center text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {users.map((user, index) => (
                <tr key={user.id} className="hover:bg-blue-50 transition-colors duration-150">
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-blue-600">
                        {index + 1}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-5 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center shadow-md">
                        <span className="text-sm font-bold text-black">
                          {user.name?.charAt(0)?.toUpperCase() || 'U'}
                        </span>
                      </div>
                      <div className="ml-4">
                        <div className="text-left text-sm font-semibold text-gray-900">
                          {user.name}
                        </div>
                        <div className="text-sm text-gray-600 flex items-center mt-1">
                          <Mail className="w-4 h-4 mr-2 text-gray-400" />
                          {user.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap ">
                    <span className={`inline-flex px-3 py-1 text-xs font-bold rounded-full  ${getUserTypeColor(user.userType)}`}>
                      {user.userType.charAt(0).toUpperCase() + user.userType.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap text-sm text-gray-600">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                      {formatDate(user.createdAt)}
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap text-center">
                    <div className="flex items-center justify-center space-x-2">
                      <button
                        onClick={() => handleViewDetailsClick(user)}
                        className="bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-700 p-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                        title="View Details"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleEditClick(user)}
                        className="bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 p-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                        title="Edit User"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteClick(user.id)}
                        className="bg-red-100 text-red-600 hover:bg-red-200 hover:text-red-700 p-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                        title="Delete User"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* User Details Modal */}
      <UserDetailsModal
        isOpen={detailsModalOpen}
        onClose={handleCloseDetailsModal}
        user={userToView}
      />

      {/* Edit User Modal */}
      <EditUserModal
        isOpen={editModalOpen}
        onClose={handleCloseEditModal}
        user={userToEdit}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteModalOpen}
        onClose={handleCloseDeleteModal}
        onConfirm={confirmDelete}
        title="Delete User"
        subtitle="Are you sure you want to delete this user? This action cannot be undone."
        confirmLabel="Delete"
        confirmColor="danger"
        isLoading={isDeleting}
      />
    </>
  )
}

export default UserTable

