import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'sonner'
import { Users, UserCheck, Building2, TrendingUp, Loader2 } from 'lucide-react'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { fetchDashboardCounts } from '../store/slices/dashboardSlice'

export default function DashboardPage() {
  const dispatch = useAppDispatch()
  const { counts, isLoading, error } = useAppSelector((state:any) => state.dashboard)
  const navigate = useNavigate()

  useEffect(() => {
    dispatch(fetchDashboardCounts())
  }, [dispatch])

  useEffect(() => {
    if (error) {
      toast.error(error)
    }
  }, [error])

  

  const statsCards = [
    {
      title: 'Total Managers',
      value: counts.managers,
      icon: Building2,
      color: 'blue',
      change: '+2.5%',
      changeText: 'from last month'
    },
    {
      title: 'Total Staff',
      value: counts.staff,
      icon: UserCheck,
      color: 'green',
      change: '+5.2%',
      changeText: 'from last month'
    },
    {
      title: 'Total Clients',
      value: counts.clients,
      icon: Users,
      color: 'purple',
      change: '+12.3%',
      changeText: 'from last month'
    }
  ]



  const quickActions = [
    {
      title: 'Manage Managers',
      icon: Building2,
      path: '/users',
      description: 'Add, edit, or remove managers'
    },
    {
      title: 'Manage Staff',
      icon: UserCheck,
      path: '/users',
      description: 'Oversee staff members'
    },
    {
      title: 'Manage Clients',
      icon: Users,
      path: '/users',
      description: 'Handle client accounts'
    }
  ]

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8 flex justify-center items-center">
        <div className=' justify-center'>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Dashboard Overview</h1>
          <p className="text-gray-600">
            Monitor your organization's key metrics and performance indicators.
          </p>
        </div>
        
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {statsCards.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div key={index} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-600">{stat.title}</h3>
                <div className={`p-2 rounded-lg bg-${stat.color}-100`}>
                  {isLoading ? (
                    <div className="w-5 h-5 bg-gray-300 rounded animate-pulse"></div>
                  ) : (
                    <Icon className={`w-5 h-5 text-${stat.color}-600`} />
                  )}
                </div>
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
                    <div className="h-8 bg-gray-300 rounded w-20 animate-pulse"></div>
                  </div>
                ) : (
                  stat.value.toLocaleString()
                )}
              </div>
              <div className="flex items-center text-sm">
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-gray-300 rounded animate-pulse"></div>
                    <div className="h-4 bg-gray-300 rounded w-16 animate-pulse"></div>
                    <div className="h-4 bg-gray-300 rounded w-24 animate-pulse"></div>
                  </div>
                ) : (
                  <>
                    <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                    <span className="text-green-600 font-medium">{stat.change}</span>
                    <span className="text-gray-500 ml-1">{stat.changeText}</span>
                  </>
                )}
              </div>
            </div>
          )
        })}
      </div>

      {/* Content Grid */}
      <div className="flex gap-6">
  {/* Quick Actions */}
  <div className="bg-white rounded-lg p-6 w-full">
    <h2 className="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h2>

    <div className="space-y-3 lg:space-y-0 lg:flex lg:justify-center lg:gap-8">
      {quickActions.map((action, index) => {
        const Icon = action.icon
        return (
          <button
            key={index}
            onClick={() => navigate(action.path)}
            className="w-full lg:w-auto flex items-center p-4 lg:p-2 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-colors text-left"
          >
            <div className="p-2 lg:p-1 bg-gray-100 rounded-lg mr-4">
              <Icon className="w-5 h-5 text-gray-600 lg:w-4 lg:h-4" />
            </div>
            <div className="text-sm lg:text-xs">
              <h3 className="font-medium text-gray-900">{action.title}</h3>
              <p className="text-sm text-gray-500 lg:text-xs">{action.description}</p>
            </div>
          </button>
        )
      })}
    </div>
  </div>
</div>

    </div>
  )
}
