import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { fetchUsers, deleteUser as deleteUser<PERSON><PERSON>, updateUser as updateUser<PERSON><PERSON>, createUser as createUser<PERSON>pi } from '../../services/api'
import type { User, CreateUserFormData } from '../../types/user'

interface UsersState {
  users: User[]
  isLoading: boolean
  isCreating: boolean
  error: string | null
  deleteLoading: string | null // ID of user being deleted
  updateLoading: string | null // ID of user being updated
}

const initialState: UsersState = {
  users: [],
  isLoading: false,
  isCreating: false,
  error: null,
  deleteLoading: null,
  updateLoading: null,
}

// Async thunk for fetching users
export const fetchAllUsers = createAsyncThunk(
  'users/fetchAll',
  async (_, { rejectWithValue }) => {
    try {
      const users = await fetchUsers()
      return users
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch users')
    }
  }
)

// Async thunk for deleting a user
export const deleteUser = createAsyncThunk(
  'users/delete',
  async (userId: string, { rejectWithValue }) => {
    try {
      await deleteUserApi(userId)
      return userId
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to delete user')
    }
  }
)

// Async thunk for updating a user
export const updateUser = createAsyncThunk(
  'users/update',
  async ({ userId, userData }: { userId: string; userData: Partial<User> }, { rejectWithValue }) => {
    try {
      const updatedUser = await updateUserApi(userId, userData)
      return updatedUser
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update user')
    }
  }
)

// Async thunk for creating a user
export const createUser = createAsyncThunk(
  'users/create',
  async (userData: CreateUserFormData, { rejectWithValue }) => {
    try {
      const newUser = await createUserApi(userData)
      return newUser
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to create user')
    }
  }
)

const usersSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    clearError: (state:any) => {
      state.error = null
    },
    setDeleteLoading: (state:any, action:any) => {
      state.deleteLoading = action.payload
    },
    setUpdateLoading: (state:any, action:any) => {
      state.updateLoading = action.payload
    },
  },
  extraReducers: (builder:any) => {
    builder
      // Fetch users
      .addCase(fetchAllUsers.pending, (state:any) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchAllUsers.fulfilled, (state:any, action:any) => {
        state.isLoading = false
        state.users = action.payload
        state.error = null
      })
      .addCase(fetchAllUsers.rejected, (state:any, action:any) => {
        state.isLoading = false
        state.error = action.payload as string
      })
      // Delete user
      .addCase(deleteUser.pending, (state:any, action:any) => {
        state.deleteLoading = action.meta.arg
      })
      .addCase(deleteUser.fulfilled, (state:any, action:any) => {
        state.deleteLoading = null
        state.users = state.users.filter((user: User) => user.id !== action.payload)
      })
      .addCase(deleteUser.rejected, (state:any, action:any) => {
        state.deleteLoading = null
        state.error = action.payload as string
      })
      // Update user
      .addCase(updateUser.pending, (state:any, action:any) => {
        state.updateLoading = action.meta.arg.userId
      })
      .addCase(updateUser.fulfilled, (state:any, action:any) => {
        state.updateLoading = null
        const updatedUser = action.payload
        const index = state.users.findIndex((user: User) => user.id === updatedUser.id)
        if (index !== -1) {
          state.users[index] = updatedUser
        }
      })
      .addCase(updateUser.rejected, (state:any, action:any) => {
        state.updateLoading = null
        state.error = action.payload as string
      })
      // Create user
      .addCase(createUser.pending, (state:any) => {
        state.isCreating = true
        state.error = null
      })
      .addCase(createUser.fulfilled, (state:any, action:any) => {
        state.isCreating = false
        state.users.push(action.payload)
        state.error = null
      })
      .addCase(createUser.rejected, (state:any, action:any) => {
        state.isCreating = false
        state.error = action.payload as string
      })
  },
})

export const { clearError, setDeleteLoading, setUpdateLoading } = usersSlice.actions
export default usersSlice.reducer

