import { useState } from 'react'
import { Edit, Trash2, Calendar, FileText, User, Tag, Paperclip } from 'lucide-react'
import { toast } from 'sonner'
import type { Issue } from '../types/issue'
import type { User as UserType } from '../types/user'
import type { Category } from '../types/category'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { deleteIssue } from '../store/slices/issuesSlice'
import ConfirmationModal from './ConfirmationModal'
import IssueDetailsModal from './IssueDetailsModal'
import EditIssueModal from './EditIssueModal'
import { formatDate } from '../utils/app.utils'
import { Eye } from 'lucide-react'

interface IssueTableProps {
  issues: Issue[]
  users: UserType[]
  categories: Category[]
}

const IssueTable = ({ issues, users, categories }: IssueTableProps) => {
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [issueToDelete, setIssueToDelete] = useState<string | null>(null)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [issueToEdit, setIssueToEdit] = useState<Issue | null>(null)
  const [detailsModalOpen, setDetailsModalOpen] = useState(false)
  const [issueToView, setIssueToView] = useState<Issue | null>(null)
  const dispatch = useAppDispatch()
  const { deleteLoading } = useAppSelector((state:any) => state.issues)

  const isDeleting = deleteLoading === issueToDelete

  // Helper function to get username from user ID
  const getUserName = (userId: string): string => {
    const user = users.find(u => u.id === userId)
    return user ? user.name : userId // Fallback to userId if user not found
  }

  const handleDeleteClick = (issueId: string) => {
    setIssueToDelete(issueId)
    setDeleteModalOpen(true)
  }

  const handleEditClick = (issue: Issue) => {
    setIssueToEdit(issue)
    setEditModalOpen(true)
  }

  const handleViewDetailsClick = (issue: Issue) => {
    setIssueToView(issue)
    setDetailsModalOpen(true)
  }

  const confirmDelete = async () => {
    if (issueToDelete) {
      try {
        await dispatch(deleteIssue(issueToDelete)).unwrap()
        toast.success('Issue deleted successfully')
        setDeleteModalOpen(false)
        setIssueToDelete(null)
      } catch (error) {
        toast.error('Failed to delete issue')
      }
    }
  }

  const handleCloseEditModal = () => {
    setEditModalOpen(false)
    setIssueToEdit(null)
  }

  const handleCloseDeleteModal = () => {
    setDeleteModalOpen(false)
    setIssueToDelete(null)
  }

  const handleCloseDetailsModal = () => {
    setDetailsModalOpen(false)
    setIssueToView(null)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-red-100 text-red-800'
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-800'
      case 'resolved':
        return 'bg-green-100 text-green-800'
      case 'closed':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }

  if (issues.length === 0) {
    return (
      <div className="text-center py-16 bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-orange-100 to-orange-200 rounded-full flex items-center justify-center">
          <FileText className="w-8 h-8 text-orange-500" />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-3">
          No issues found
        </h3>
        <p className="text-gray-600 max-w-md mx-auto">
          There are no issues in the system yet. Issues will appear here when they are reported.
        </p>
      </div>
    )
  }

  return (
    <>
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  #
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Issue Details
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Categories
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider whitespace-nowrap">
                  Date Created
                </th>
                <th className="px-6 py-4 text-center text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {issues.map((issue, index) => (
                <tr key={issue.id} className="hover:bg-orange-50 transition-colors duration-150">
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-orange-600">
                        {index + 1}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-5">
                    <div className="flex items-start">
                      <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center shadow-md">
                        <FileText className="w-6 h-6 text-gray-600" />
                      </div>
                      <div className="ml-4 flex-1">
                        <div className="text-left text-sm font-semibold text-gray-900 mb-1">
                          {truncateText(issue.note, 13)}
                        </div>
                        {issue.attachment && issue.attachment.length > 0 && (
                          <div className="flex items-center text-xs text-gray-500 mt-1">
                            <Paperclip className="w-3 h-3 mr-1" />
                            {issue.attachment.length} attachment{issue.attachment.length > 1 ? 's' : ''}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <span className={`inline-flex px-3 py-1 text-xs font-bold rounded-full ${getStatusColor(issue.status)}`}>
                      {issue.status.charAt(0).toUpperCase() + issue.status.slice(1).replace('-', ' ')}
                    </span>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap text-sm text-gray-600">
                    <div className="flex items-center">
                      <User className="w-4 h-4 mr-2 text-gray-400" />
                      <div>
                        <div className="font-medium text-gray-900 ">{getUserName(issue.userId)}</div>
                        {/* <div className="text-xs text-gray-500">ID: {issue.userId}</div> */}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="flex items-center">
                      <Tag className="w-4 h-4 mr-2 text-gray-400" />
                      <div className="text-sm text-gray-600">
                        {issue.categories.length > 0 ? (
                          <span>{issue.categories.length} categor{issue.categories.length > 1 ? 'ies' : 'y'}</span>
                        ) : (
                          <span className="text-gray-400">No categories</span>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap text-sm text-gray-600">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                      {formatDate(issue.createdAt)}
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap text-center">
                    <div className="flex items-center justify-center space-x-2">
                      <button
                        onClick={() => handleViewDetailsClick(issue)}
                        className="bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-700 p-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                        title="View Details"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleEditClick(issue)}
                        className="bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 p-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                        title="Edit Issue"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteClick(issue.id)}
                        className="bg-red-100 text-red-600 hover:bg-red-200 hover:text-red-700 p-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                        title="Delete Issue"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Issue Details Modal */}
      <IssueDetailsModal
        isOpen={detailsModalOpen}
        onClose={handleCloseDetailsModal}
        issue={issueToView}
        users={users}
        categories={categories}
      />

      {/* Edit Issue Modal */}
      <EditIssueModal
        isOpen={editModalOpen}
        onClose={handleCloseEditModal}
        issue={issueToEdit}
        categories={categories}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteModalOpen}
        onClose={handleCloseDeleteModal}
        onConfirm={confirmDelete}
        title="Delete Issue"
        subtitle="Are you sure you want to delete this issue? This action cannot be undone."
        confirmLabel="Delete"
        confirmColor="danger"
        isLoading={isDeleting}
      />
    </>
  )
}

export default IssueTable
