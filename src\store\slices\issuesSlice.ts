import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { fetchIssues, deleteIssue as deleteIssue<PERSON><PERSON>, updateIssue as updateIssue<PERSON><PERSON>, createIssue as createIssue<PERSON><PERSON> } from '../../services/api'
import type { Issue, CreateIssueFormData } from '../../types/issue'

interface IssuesState {
  issues: Issue[]
  isLoading: boolean
  isCreating: boolean
  error: string | null
  deleteLoading: string | null // ID of issue being deleted
  updateLoading: string | null // ID of issue being updated
}

const initialState: IssuesState = {
  issues: [],
  isLoading: false,
  isCreating: false,
  error: null,
  deleteLoading: null,
  updateLoading: null,
}

// Async thunk for fetching issues
export const fetchAllIssues = createAsyncThunk(
  'issues/fetchAll',
  async (_, { rejectWithValue }) => {
    try {
      const issues = await fetchIssues()
      return issues
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch issues')
    }
  }
)

// Async thunk for deleting an issue
export const deleteIssue = createAsyncThunk(
  'issues/delete',
  async (issueId: string, { rejectWithValue }) => {
    try {
      await deleteIssueApi(issueId)
      return issueId
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to delete issue')
    }
  }
)

// Async thunk for updating an issue
export const updateIssue = createAsyncThunk(
  'issues/update',
  async ({ issueId, issueData }: { issueId: string; issueData: Partial<Issue> }, { rejectWithValue }) => {
    try {
      const updatedIssue = await updateIssueApi(issueId, issueData)
      return updatedIssue
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update issue')
    }
  }
)

// Async thunk for creating an issue
export const createIssue = createAsyncThunk(
  'issues/create',
  async (issueData: CreateIssueFormData, { rejectWithValue }) => {
    try {
      const newIssue = await createIssueApi(issueData)
      return newIssue
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to create issue')
    }
  }
)

const issuesSlice = createSlice({
  name: 'issues',
  initialState,
  reducers: {
    clearError: (state:any) => {
      state.error = null
    },
    setDeleteLoading: (state:any, action:any) => {
      state.deleteLoading = action.payload
    },
    setUpdateLoading: (state:any, action:any) => {
      state.updateLoading = action.payload
    },
  },
  extraReducers: (builder:any) => {
    builder
      // Fetch issues
      .addCase(fetchAllIssues.pending, (state:any) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchAllIssues.fulfilled, (state:any, action:any) => {
        state.isLoading = false
        state.issues = action.payload
        state.error = null
      })
      .addCase(fetchAllIssues.rejected, (state:any, action:any) => {
        state.isLoading = false
        state.error = action.payload as string
      })
      // Delete issue
      .addCase(deleteIssue.pending, (state:any, action:any) => {
        state.deleteLoading = action.meta.arg
      })
      .addCase(deleteIssue.fulfilled, (state:any, action:any) => {
        state.deleteLoading = null
        state.issues = state.issues.filter((issue: Issue) => issue.id !== action.payload)
      })
      .addCase(deleteIssue.rejected, (state:any, action:any) => {
        state.deleteLoading = null
        state.error = action.payload as string
      })
      // Update issue
      .addCase(updateIssue.pending, (state:any, action:any) => {
        state.updateLoading = action.meta.arg.issueId
      })
      .addCase(updateIssue.fulfilled, (state:any, action:any) => {
        state.updateLoading = null
        const updatedIssue = action.payload
        const index = state.issues.findIndex((issue: Issue) => issue.id === updatedIssue.id)
        if (index !== -1) {
          state.issues[index] = updatedIssue
        }
      })
      .addCase(updateIssue.rejected, (state:any, action:any) => {
        state.updateLoading = null
        state.error = action.payload as string
      })
      // Create issue
      .addCase(createIssue.pending, (state:any) => {
        state.isCreating = true
        state.error = null
      })
      .addCase(createIssue.fulfilled, (state:any, action:any) => {
        state.isCreating = false
        state.issues.push(action.payload)
        state.error = null
      })
      .addCase(createIssue.rejected, (state:any, action:any) => {
        state.isCreating = false
        state.error = action.payload as string
      })
  },
})

export const { clearError, setDeleteLoading, setUpdateLoading } = issuesSlice.actions
export default issuesSlice.reducer
