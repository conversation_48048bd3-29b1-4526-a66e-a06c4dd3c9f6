import { useState, useEffect } from 'react'
import { X, FileText, Tag, Save } from 'lucide-react'
import { toast } from 'sonner'
import type { Issue } from '../types/issue'
import type { Category } from '../types/category'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { updateIssue } from '../store/slices/issuesSlice'

interface EditIssueModalProps {
  isOpen: boolean
  onClose: () => void
  issue: Issue | null
  categories: Category[]
}

interface FormData {
  note: string
  status: 'open' | 'in-progress' | 'resolved' | 'closed'
  categories: string[]
}

interface FormErrors {
  note?: string
  status?: string
  categories?: string
}

export default function EditIssueModal({ isOpen, onClose, issue, categories }: EditIssueModalProps) {
  const dispatch = useAppDispatch()
  const { updateLoading } = useAppSelector((state: any) => state.issues)
  
  const [formData, setFormData] = useState<FormData>({
    note: '',
    status: 'open',
    categories: [],
  })
  
  const [errors, setErrors] = useState<FormErrors>({})
  
  const isUpdating = updateLoading === issue?.id

  // Set form data when issue prop changes
  useEffect(() => {
    if (issue) {
      setFormData({
        note: issue.note || '',
        status: issue.status || 'open',
        categories: issue.categories || [],
      })
    }
  }, [issue])

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.note.trim()) {
      newErrors.note = 'Issue description is required'
    }

    if (!formData.status) {
      newErrors.status = 'Status is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!issue) return

    if (validateForm()) {
      try {
        await dispatch(updateIssue({ 
          issueId: issue.id, 
          issueData: {
            ...formData,
            updatedAt: new Date().toISOString()
          }
        })).unwrap()
        
        toast.success('Issue updated successfully')
        onClose()
      } catch (error) {
        toast.error('Failed to update issue')
      }
    }
  }

  const handleClose = () => {
    setFormData({
      note: '',
      status: 'open',
      categories: [],
    })
    setErrors({})
    onClose()
  }

  const handleCategoryToggle = (categoryId: string) => {
    setFormData(prev => ({
      ...prev,
      categories: prev.categories.includes(categoryId)
        ? prev.categories.filter(id => id !== categoryId)
        : [...prev.categories, categoryId]
    }))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'resolved':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'closed':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  if (!isOpen || !issue) return null

  return (
    <div className="fixed inset-0 bg-gray-900/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-[#BE935E] px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center">
              <FileText className="w-6 h-6 text-orange-500" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Edit Issue</h2>
              <p className="text-orange-100 text-sm">Update issue details and status</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="text-orange-100 hover:text-white transition-colors p-2 rounded-lg"
            disabled={isUpdating}
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Body */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Issue Description */}
          <div>
            <label className="flex text-sm font-medium text-gray-700 mb-2">
              Issue Description *
            </label>
            <textarea
              value={formData.note}
              onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}
              rows={4}
              className={`w-full px-3 py-2 border text-left rounded-lg focus:outline-none focus:ring-2 focus:ring-[#BE935E] focus:border-transparent ${
                errors.note ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Describe the issue in detail..." 
              disabled={isUpdating}
            />
            {errors.note && (
              <p className="mt-1 text-sm text-red-600">{errors.note}</p>
            )}
          </div>

          {/* Status */}
          <div>
            <label className="flex text-sm font-medium text-gray-700 mb-2">
              Status *
            </label>
            <div className="grid grid-cols-2 gap-3">
              {['open', 'in-progress', 'resolved', 'closed'].map((status) => (
                <label
                  key={status}
                  className={`flex items-center p-3 border rounded-lg cursor-pointer transition-all ${
                    formData.status === status
                      ? 'border-[#BE935E] bg-orange-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="status"
                    value={status}
                    checked={formData.status === status}
                    onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                    className="sr-only"
                    disabled={isUpdating}
                  />
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${getStatusColor(status).split(' ')[0]}`} />
                    <span className="text-sm font-medium capitalize">
                      {status.replace('-', ' ')}
                    </span>
                  </div>
                </label>
              ))}
            </div>
            {errors.status && (
              <p className="mt-1 text-sm text-red-600">{errors.status}</p>
            )}
          </div>

          {/* Categories */}
          <div>
            <label className="flex text-sm font-medium text-gray-700 mb-2">
              Categories
            </label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-40 overflow-y-auto">
              {categories.map((category) => (
                <label
                  key={category.id}
                  className={`flex items-center p-2 border rounded-lg cursor-pointer transition-all ${
                    formData.categories.includes(category.id)
                      ? 'border-[#BE935E] bg-orange-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="checkbox"
                    checked={formData.categories.includes(category.id)}
                    onChange={() => handleCategoryToggle(category.id)}
                    className="sr-only"
                    disabled={isUpdating}
                  />
                  <div className="flex items-center space-x-2">
                    {category.icon?.url ? (
                      <img
                        src={category.icon.url}
                        alt={category.title}
                        className="w-4 h-4 rounded object-cover"
                      />
                    ) : (
                      <Tag className="w-4 h-4 text-gray-400" />
                    )}
                    <span className="text-sm font-medium">{category.title}</span>
                  </div>
                </label>
              ))}
            </div>
            {categories.length === 0 && (
              <p className="text-sm text-gray-500 italic">No categories available</p>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              disabled={isUpdating}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isUpdating}
              className="flex items-center space-x-2 px-4 py-2 bg-[#BE935E] text-white rounded-lg hover:bg-[#A67C52] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isUpdating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Updating...</span>
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  <span>Update Issue</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
