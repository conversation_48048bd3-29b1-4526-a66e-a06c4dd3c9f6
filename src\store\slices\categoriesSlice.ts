import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { 
  fetchCategories as fetchCategoriesApi, 
  addCategory as addCategoryApi, 
  updateCategory as updateCategoryApi, 
  deleteCategory as deleteCategoryApi 
} from '../../services/api'
import type { Category, CategoryFormData } from '../../types/category'

interface CategoriesState {
  categories: Category[]
  isLoading: boolean
  isAdding: boolean
  isUpdating: boolean
  isDeleting: boolean
  error: string | null
}

const initialState: CategoriesState = {
  categories: [],
  isLoading: false,
  isAdding: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
}

// Async thunk for fetching categories
export const fetchCategories = createAsyncThunk(
  'categories/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      const categories = await fetchCategoriesApi()
      return categories
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch categories')
    }
  }
)

// Async thunk for adding a category
export const addCategory = createAsyncThunk(
  'categories/addCategory',
  async (categoryData: CategoryFormData, { rejectWithValue }) => {
    try {
      const newCategory = await addCategoryApi(categoryData)
      return newCategory
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to add category')
    }
  }
)

// Async thunk for updating a category
export const updateCategory = createAsyncThunk(
  'categories/updateCategory',
  async ({ categoryId, categoryData }: { categoryId: string; categoryData: any }, { rejectWithValue }) => {
    try {
      const updatedCategory = await updateCategoryApi(categoryId, categoryData)
      return updatedCategory
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update category')
    }
  }
)

// Async thunk for deleting a category
export const deleteCategory = createAsyncThunk(
  'categories/deleteCategory',
  async (categoryId: string, { rejectWithValue }) => {
    try {
      const result = await deleteCategoryApi(categoryId)
      return result
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to delete category')
    }
  }
)

const categoriesSlice = createSlice({
  name: 'categories',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch categories
      .addCase(fetchCategories.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.isLoading = false
        state.categories = action.payload
        state.error = null
      })
      .addCase(fetchCategories.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
      // Add category
      .addCase(addCategory.pending, (state) => {
        state.isAdding = true
        state.error = null
      })
      .addCase(addCategory.fulfilled, (state) => {
        state.isAdding = false
        // Since we create subcategories separately, we need to refetch all categories
        // For now, just mark as successful - the component will refetch
        state.error = null
      })
      .addCase(addCategory.rejected, (state, action) => {
        state.isAdding = false
        state.error = action.payload as string
      })
      // Update category
      .addCase(updateCategory.pending, (state) => {
        state.isUpdating = true
        state.error = null
      })
      .addCase(updateCategory.fulfilled, (state) => {
        state.isUpdating = false
        // Since subcategories are handled separately, we need to refetch categories
        // For now, just mark as successful - the component will refetch
        state.error = null
      })
      .addCase(updateCategory.rejected, (state, action) => {
        state.isUpdating = false
        state.error = action.payload as string
      })
      // Delete category
      .addCase(deleteCategory.pending, (state) => {
        state.isDeleting = true
        state.error = null
      })
      .addCase(deleteCategory.fulfilled, (state, action) => {
        state.isDeleting = false
        const { parentId, subcategoryIds } = action.payload

        // Remove the parent category and all its subcategories from the flat array
        state.categories = state.categories.filter(cat =>
          cat.id !== parentId && !subcategoryIds.includes(cat.id)
        )

        state.error = null
      })
      .addCase(deleteCategory.rejected, (state, action) => {
        state.isDeleting = false
        state.error = action.payload as string
      })
  },
})

export const { clearError } = categoriesSlice.actions
export default categoriesSlice.reducer
