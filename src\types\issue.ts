export interface Attachment {
  path: string
  url: string
}

export interface Issue {
  id: string
  attachment: Attachment[]
  categories: string[]
  note: string
  status: 'open' | 'in-progress' | 'resolved' | 'closed'
  userId: string
  createdAt?: string
  updatedAt?: string
}

export interface CreateIssueFormData {
  attachment: Attachment[]
  categories: string[]
  note: string
  status: 'open' | 'in-progress' | 'resolved' | 'closed'
  userId: string
}
