import { doc, getDoc } from 'firebase/firestore'
import { db } from '../config/firebase.config'

export async function isLoggedIn(): Promise<boolean> {
  const userId = localStorage.getItem('userId')
  if (!userId) {
    return false
  }
  // CHECK USER IS ADMIN
  const userData = await fetchUser(userId)
  if (userData && userData?.userType === 'admin') {
    return true
  }
  return false
}

// GET USER THROUGH UUID
export async function fetchUser(id: string): Promise<any> {
  const userRef = doc(db, 'Users', id)
  const userSnap = await getDoc(userRef)
  if (userSnap.exists()) {
    return userSnap.data()
  } else return null
}

// GET COUNTS FOR DASHBOARD - Now handled by Redux, but keeping for backward compatibility
export async function getDashboardCounts(): Promise<{
  managers: number
  staff: number
  clients: number
}> {
  // Import the API function to maintain consistency
  const { getDashboardCounts: getCountsFromAPI } = await import('../services/api')
  return getCountsFromAPI()
}

// Format date consistently throughout the app as MM/DD/YYYY
export function formatDate(dateInput?: string | null | any): string {
  if (!dateInput || dateInput === 'Invalid Date') return 'N/A';

  try {
    let date: Date;

    // Handle different input types
    if (typeof dateInput === 'string') {
      date = new Date(dateInput);
    } else if (dateInput && typeof dateInput.toDate === 'function') {
      // Firestore Timestamp
      date = dateInput.toDate();
    } else if (dateInput instanceof Date) {
      date = dateInput;
    } else {
      return 'N/A';
    }

    // Check if date is valid
    if (isNaN(date.getTime())) return 'N/A';

    // Format as MM/DD/YYYY
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const year = date.getFullYear();

    return `${month}/${day}/${year}`;
  } catch (error) {
    console.error('Error formatting date:', error, 'Input:', dateInput);
    return 'N/A';
  }
}

