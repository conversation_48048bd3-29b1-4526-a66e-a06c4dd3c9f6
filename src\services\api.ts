import { collection, getDocs, doc, deleteDoc, updateDoc, addDoc, query, where, getCountFromServer, getDoc, serverTimestamp } from 'firebase/firestore'
import { httpsCallable } from 'firebase/functions'
import { db, functions } from '../config/firebase.config'
import type { User, CreateUserFormData } from '../types/user'
import type { Category, CategoryFormData } from '../types/category'
import type { Issue, CreateIssueFormData } from '../types/issue'

// Utility function to convert Firestore Timestamp to ISO string
function convertTimestampToISO(timestamp: any): string {
  if (!timestamp) return new Date().toISOString();

  // If it's already a string, return it
  if (typeof timestamp === 'string') return timestamp;

  // If it's a Firestore Timestamp, convert it
  if (timestamp && typeof timestamp.toDate === 'function') {
    return timestamp.toDate().toISOString();
  }

  // If it's a Date object, convert it
  if (timestamp instanceof Date) {
    return timestamp.toISOString();
  }

  // Fallback
  return new Date().toISOString();
}

// Fetch dashboard counts from Firestore
export async function getDashboardCounts(): Promise<{
  managers: number
  staff: number
  clients: number
}> {
  try {
    const usersRef = collection(db, 'Users')

    // Get count of managers
    const managersQuery = query(usersRef, where('userType', '==', 'manager'))
    const managersSnapshot = await getCountFromServer(managersQuery)
    const managers = managersSnapshot.data().count

    // Get count of staff
    const staffQuery = query(usersRef, where('userType', '==', 'staff'))
    const staffSnapshot = await getCountFromServer(staffQuery)
    const staff = staffSnapshot.data().count

    // Get count of clients
    const clientsQuery = query(usersRef, where('userType', '==', 'client'))
    const clientsSnapshot = await getCountFromServer(clientsQuery)
    const clients = clientsSnapshot.data().count

    return {
      managers,
      staff,
      clients,
    }
  } catch (error) {
    console.error('Error fetching dashboard counts:', error)
    // Return fallback data if Firebase fails
    return {
      managers: 0,
      staff: 0,
      clients: 0,
    }
  }
}

// Fetch all users from Firestore
export async function fetchUsers(): Promise<User[]> {
  try {
    const usersRef = collection(db, 'Users')
    const querySnapshot = await getDocs(usersRef)

    const users: User[] = []
    querySnapshot.forEach((doc) => {
      const userData = doc.data()
      // Only include users that are not admins
      if (userData.userType !== 'admin') {
        users.push({
          id: doc.id,
          name: userData.name || 'Unknown',
          email: userData.email || '',
          userType: userData.userType || 'client',
          assignedCategories: userData.assignedCategories || undefined,
          createdAt: convertTimestampToISO(userData.createdAt),
          updatedAt: convertTimestampToISO(userData.updatedAt || userData.createdAt),
        })
      }
    })

    return users
  } catch (error) {
    console.error('Error fetching users:', error)
    throw new Error('Failed to fetch users from database')
  }
}

// Delete a user from Firestore
export async function deleteUser(userId: string): Promise<void> {
  try {
    const userRef = doc(db, 'Users', userId)
    await deleteDoc(userRef)
  } catch (error) {
    console.error('Error deleting user:', error)
    throw new Error('Failed to delete user from database')
  }
}

// Update a user in Firestore
export async function updateUser(userId: string, userData: Partial<User>): Promise<User> {
  try {
    const userRef = doc(db, 'Users', userId)

    // We'll get the updated data after the update to ensure we have the latest timestamps

    const updateData = {
      ...userData,
      updatedAt: serverTimestamp(),
    }

    // Remove id from update data if present
    const { id, ...dataToUpdate } = updateData as any

    await updateDoc(userRef, dataToUpdate)

    // Get the updated document to get the actual server timestamp
    const updatedDoc = await getDoc(userRef)
    const updatedData = updatedDoc.data()

    // Return the updated user data with properly converted timestamps
    return {
      id: userId,
      ...updatedData,
      createdAt: convertTimestampToISO(updatedData?.createdAt),
      updatedAt: convertTimestampToISO(updatedData?.updatedAt),
    } as User
  } catch (error) {
    console.error('Error updating user:', error)
    throw new Error('Failed to update user in database')
  }
}

// Fetch users by type
export async function fetchUsersByType(userType: 'manager' | 'staff' | 'client'): Promise<User[]> {
  try {
    const usersRef = collection(db, 'Users')
    const q = query(usersRef, where('userType', '==', userType))
    const querySnapshot = await getDocs(q)

    const users: User[] = []
    querySnapshot.forEach((doc) => {
      const userData = doc.data()
      users.push({
        id: doc.id,
        name: userData.name || 'Unknown',
        email: userData.email || '',
        userType: userData.userType || userType,
        assignedCategories: userData.assignedCategories || undefined,
        createdAt: convertTimestampToISO(userData.createdAt),
        updatedAt: convertTimestampToISO(userData.updatedAt || userData.createdAt),
      })
    })

    return users
  } catch (error) {
    console.error(`Error fetching ${userType}s:`, error)
    throw new Error(`Failed to fetch ${userType}s from database`)
  }
}

// Create a new user using Firebase Cloud Function
export async function createUser(userData: CreateUserFormData): Promise<User> {
  try {
    const createUserFunction = httpsCallable(functions, 'createUser')
    const result = await createUserFunction(userData)

    if (result.data && (result.data as any).success) {
      return (result.data as any).user as User
    } else {
      throw new Error((result.data as any).message || 'Failed to create user')
    }
  } catch (error: any) {
    console.error('Error creating user:', error)

    // Handle Firebase function errors
    if (error.code === 'functions/internal') {
      throw new Error(error.message || 'Failed to create user')
    } else if (error.code === 'functions/unauthenticated') {
      throw new Error('You must be authenticated to create users')
    } else {
      throw new Error(error.message || 'Failed to create user')
    }
  }
}

// Categories API Functions

// Fetch all categories from Firestore
export async function fetchCategories(): Promise<Category[]> {
  try {
    const categoriesRef = collection(db, 'Categories')
    const querySnapshot = await getDocs(categoriesRef)

    // Create a flat array of all categories
    const allCategories: Category[] = []

    querySnapshot.forEach((doc) => {
      const data = doc.data()

      const categoryData: Category = {
        id: doc.id,
        title: data.title || '',
        icon: data.icon || { url: '' },
        parentCategory: data.parentCategory,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt
      }

      allCategories.push(categoryData)
    })

    return allCategories
  } catch (error) {
    console.error('Error fetching categories:', error)
    throw new Error('Failed to fetch categories from database')
  }
}

// Add a new category to Firestore
export async function addCategory(categoryData: CategoryFormData): Promise<Category> {
  try {
    const categoriesRef = collection(db, 'Categories')

    // Create the main category without subcategories field
    const mainCategoryData = {
      title: categoryData.title,
      icon: categoryData.icon,
      createdAt: new Date().toISOString(),
    }

    const docRef = await addDoc(categoriesRef, mainCategoryData)
    const parentCategoryId = docRef.id

    // Create subcategory documents if any exist
    const subcategoryPromises = categoryData.subcategories.map(async (subcategoryTitle) => {
      const subcategoryData = {
        title: subcategoryTitle,
        parentCategory: parentCategoryId,
        createdAt: new Date().toISOString(),
      }
      return addDoc(categoriesRef, subcategoryData)
    })

    // Wait for all subcategory documents to be created
    await Promise.all(subcategoryPromises)

    // Return just the parent category - subcategories will be fetched separately
    return {
      id: parentCategoryId,
      ...mainCategoryData,
    } as Category
  } catch (error) {
    console.error('Error adding category:', error)
    throw new Error('Failed to add category to database')
  }
}

// Update a category in Firestore
export async function updateCategory(categoryId: string, categoryData: Partial<Category> & { subcategories?: string[] }): Promise<Category> {
  try {
    const categoryRef = doc(db, 'Categories', categoryId)
    const categoriesRef = collection(db, 'Categories')

    // Update the main category data (excluding subcategories)
    const { subcategories, id, ...mainUpdateData } = categoryData
    const updateData = {
      ...mainUpdateData,
      updatedAt: new Date().toISOString(),
    }

    await updateDoc(categoryRef, updateData)

    // Handle subcategories if provided
    if (subcategories !== undefined) {
      // Get existing subcategories for this parent
      const allCategoriesSnapshot = await getDocs(categoriesRef)
      const existingSubcategories: Array<{ id: string; title: string; parentCategory: string }> = []

      allCategoriesSnapshot.forEach((doc) => {
        const data = doc.data()
        if (data.parentCategory === categoryId) {
          existingSubcategories.push({
            id: doc.id,
            title: data.title,
            parentCategory: data.parentCategory
          })
        }
      })

      const existingSubTitles = existingSubcategories.map(sub => sub.title)
      const newSubTitles = subcategories

      // Find subcategories to add
      const subcategoriesToAdd = newSubTitles.filter((title: string) => !existingSubTitles.includes(title))

      // Find subcategories to remove
      const subcategoriesToRemove = existingSubcategories.filter(sub => !newSubTitles.includes(sub.title))

      // Add new subcategories
      const addPromises = subcategoriesToAdd.map(async (subcategoryTitle: string) => {
        const subcategoryData = {
          title: subcategoryTitle,
          parentCategory: categoryId,
          createdAt: new Date().toISOString(),
        }
        return addDoc(categoriesRef, subcategoryData)
      })

      // Remove deleted subcategories
      const removePromises = subcategoriesToRemove.map(async (subcategory) => {
        return deleteDoc(doc(db, 'Categories', subcategory.id))
      })

      // Execute all operations
      await Promise.all([...addPromises, ...removePromises])
    }

    return {
      id: categoryId,
      ...categoryData,
      updatedAt: updateData.updatedAt,
    } as Category
  } catch (error) {
    console.error('Error updating category:', error)
    throw new Error('Failed to update category in database')
  }
}

// Delete a category from Firestore
export async function deleteCategory(categoryId: string): Promise<{ parentId: string; subcategoryIds: string[] }> {
  try {
    // First, get all categories to find subcategories
    const categories = await fetchCategories()

    // Find subcategories that belong to this parent
    const subcategories = categories.filter(cat => cat.parentCategory === categoryId)

    // Delete the parent category
    const categoryRef = doc(db, 'Categories', categoryId)
    await deleteDoc(categoryRef)

    // Delete all subcategories
    const deletePromises = subcategories.map((subcategory) =>
      deleteDoc(doc(db, 'Categories', subcategory.id))
    )

    await Promise.all(deletePromises)

    return {
      parentId: categoryId,
      subcategoryIds: subcategories.map((sub) => sub.id),
    }
  } catch (error) {
    console.error('Error deleting category:', error)
    throw new Error('Failed to delete category from database')
  }
}

// Issues API Functions

// Fetch all issues from Firestore
export async function fetchIssues(): Promise<Issue[]> {
  try {
    const issuesRef = collection(db, 'Issues')
    const querySnapshot = await getDocs(issuesRef)

    const issues: Issue[] = []
    querySnapshot.forEach((doc) => {
      const issueData = doc.data()
      issues.push({
        id: doc.id,
        attachment: issueData.attachment || [],
        categories: issueData.categories || [],
        note: issueData.note || '',
        status: issueData.status || 'open',
        userId: issueData.userId || '',
        createdAt: convertTimestampToISO(issueData.createdAt),
        updatedAt: convertTimestampToISO(issueData.updatedAt || issueData.createdAt),
      })
    })

    return issues
  } catch (error) {
    console.error('Error fetching issues:', error)
    throw new Error('Failed to fetch issues from database')
  }
}

// Delete an issue from Firestore
export async function deleteIssue(issueId: string): Promise<void> {
  try {
    const issueRef = doc(db, 'Issues', issueId)
    await deleteDoc(issueRef)
  } catch (error) {
    console.error('Error deleting issue:', error)
    throw new Error('Failed to delete issue from database')
  }
}

// Update an issue in Firestore
export async function updateIssue(issueId: string, issueData: Partial<Issue>): Promise<Issue> {
  try {
    const issueRef = doc(db, 'Issues', issueId)

    const updateData = {
      ...issueData,
      updatedAt: serverTimestamp(),
    }

    // Remove id from update data if present
    const { id, ...dataToUpdate } = updateData as any

    await updateDoc(issueRef, dataToUpdate)

    // Get the updated document to get the actual server timestamp
    const updatedDoc = await getDoc(issueRef)
    const updatedData = updatedDoc.data()

    // Return the updated issue data with properly converted timestamps
    return {
      id: issueId,
      ...updatedData,
      createdAt: convertTimestampToISO(updatedData?.createdAt),
      updatedAt: convertTimestampToISO(updatedData?.updatedAt),
    } as Issue
  } catch (error) {
    console.error('Error updating issue:', error)
    throw new Error('Failed to update issue in database')
  }
}

// Create a new issue in Firestore
export async function createIssue(issueData: CreateIssueFormData): Promise<Issue> {
  try {
    const issuesRef = collection(db, 'Issues')

    const newIssueData = {
      ...issueData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    }

    const docRef = await addDoc(issuesRef, newIssueData)

    // Get the created document to get the actual server timestamp
    const createdDoc = await getDoc(docRef)
    const createdData = createdDoc.data()

    return {
      id: docRef.id,
      ...createdData,
      createdAt: convertTimestampToISO(createdData?.createdAt),
      updatedAt: convertTimestampToISO(createdData?.updatedAt),
    } as Issue
  } catch (error) {
    console.error('Error creating issue:', error)
    throw new Error('Failed to create issue in database')
  }
}

