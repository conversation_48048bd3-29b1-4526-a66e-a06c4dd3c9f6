import { useState, useEffect } from 'react'
import { X, User, Mail, Calendar, UserCheck, FolderOpen } from 'lucide-react'
import type { User as UserType } from '../types/user'
import type { Category } from '../types/category'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { fetchCategories } from '../store/slices/categoriesSlice'
import { formatDate } from '../utils/app.utils'

interface UserDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  user: UserType | null
}

const UserDetailsModal = ({ isOpen, onClose, user }: UserDetailsModalProps) => {
  const dispatch = useAppDispatch()
  const { categories } = useAppSelector((state: any) => state.categories)
  const [assignedCategoryDetails, setAssignedCategoryDetails] = useState<Category[]>([])

  useEffect(() => {
    if (isOpen && categories.length === 0) {
      dispatch(fetchCategories())
    }
  }, [isOpen, categories.length, dispatch])

  useEffect(() => {
    if (user && user.userType === 'manager' && user.assignedCategories && categories.length > 0) {
      const categoryDetails = user.assignedCategories
        .map(categoryId => categories.find((cat: Category) => cat.id === categoryId))
        .filter(Boolean) as Category[]
      setAssignedCategoryDetails(categoryDetails)
    } else {
      setAssignedCategoryDetails([])
    }
  }, [user, categories])

  const getUserTypeColor = (type: string) => {
    switch (type) {
      case 'manager':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'staff':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'client':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const handleClose = () => {
    setAssignedCategoryDetails([])
    onClose()
  }

  if (!isOpen || !user) return null

  return (
    <div className="fixed inset-0 bg-gray-100/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-[#BE935E] px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white  rounded-full flex items-center justify-center">
              <User className="w-6 h-6 text-gray" />
            </div>
            <div>
              <h2 className="text-xl text-left font-bold text-white">User Details</h2>
              <p className="text-blue-100 text-sm">View user information and assignments</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-100 cursor-pointer hover:bg-opacity-20 p-2 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Body */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* User Avatar and Basic Info */}
          <div className="flex items-center space-x-4 mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="w-16 h-16 bg-[#BE935E] rounded-full flex items-center justify-center shadow-lg">
              <span className="text-xl font-bold text-white text-left">
                {user.name?.charAt(0)?.toUpperCase() || 'U'}
              </span>
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-bold text-gray-900 text-left">{user.name}</h3>
              <div className="flex items-center space-x-2 mt-1">
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full border ${getUserTypeColor(user.userType)}`}>
                  {user.userType.charAt(0).toUpperCase() + user.userType.slice(1)}
                </span>
              </div>
            </div>
          </div>

          {/* User Information Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {/* Email */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Mail className="w-5 h-5 text-blue-600" />
                </div>  
                <div>
                  <p className="text-sm font-medium text-gray-500">Email Address</p>
                  <p className="text-sm font-semibold text-gray-900">{user.email}</p>
                </div>
              </div>
            </div>

            {/* User Type */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <UserCheck className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">User Type</p>
                  <p className="text-sm font-semibold text-gray-900">
                    {user.userType.charAt(0).toUpperCase() + user.userType.slice(1)}
                  </p>
                </div>
              </div>
            </div>

            {/* Created Date */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Date Created</p>
                  <p className="text-sm font-semibold text-gray-900">
                    {formatDate(user.createdAt)}
                  </p>
                </div>
              </div>
            </div>

            {/* Updated Date */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Last Updated</p>
                  <p className="text-sm font-semibold text-gray-900">
                    {formatDate(user.updatedAt)}
                  </p>
                </div>
              </div>
            </div>


          </div>

          {/* Assigned Categories (Only for Managers) */}
          {user.userType === 'manager' && (
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <FolderOpen className="w-5 h-5 text-orange-600" />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">Assigned Categories</h4>
                  <p className="text-sm text-gray-500">Categories this manager is responsible for</p>
                </div>
              </div>

              {assignedCategoryDetails.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {assignedCategoryDetails.map((category) => (
                    <div key={category.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border border-gray-100">
                      {category.icon?.url ? (
                        <img
                          src={category.icon.url}
                          alt={category.title}
                          className="w-6 h-6 rounded-lg object-cover flex-shrink-0"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            target.nextElementSibling?.classList.remove('hidden');
                          }}
                        />
                      ) : null}
                      <div className={`w-8 h-8 bg-orange-200 rounded-lg flex items-center justify-center flex-shrink-0 ${category.icon?.url ? 'hidden' : ''}`}>
                        <FolderOpen className="w-4 h-4 text-orange-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-semibold text-gray-900 truncate">{category.title}</p>
                        {/* <p className="text-xs text-gray-500">Category ID: {category.id.substring(0, 8)}...</p> */}
                      </div>
                    </div>
                  ))}
                </div>
              ) : user.assignedCategories && user.assignedCategories.length > 0 ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  <p className="text-sm text-gray-500">Loading category details...</p>
                </div>
              ) : (
                <div className="text-center py-8">
                  <FolderOpen className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-sm text-gray-500">No categories assigned to this manager</p>
                </div>
              )}
            </div>
          )}
        </div>

        
      </div>
    </div>
  )
}

export default UserDetailsModal

