export interface User {
  id: string
  name: string
  email: string
  userType: 'manager' | 'staff' | 'client'
  assignedCategories?: string[] // For managers - array of category IDs
  createdAt?: string
  updatedAt?: string
}

export interface CreateUserFormData {
  name: string
  email: string
  password: string
  userType: 'manager' | 'staff' | 'client'
  assignedCategories?: string[] // For managers
}
