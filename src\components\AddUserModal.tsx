import React, { useState, useEffect } from 'react'
import { X, User, Mail, Lock, UserCheck, Check } from 'lucide-react'
import { toast } from 'sonner'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { createUser } from '../store/slices/usersSlice'
import { fetchCategories } from '../store/slices/categoriesSlice'
import type { CreateUserFormData } from '../types/user'
import type { Category } from '../types/category'

interface AddUserModalProps {
  isOpen: boolean
  onClose: () => void
}

interface FormErrors {
  name?: string
  email?: string
  password?: string
  userType?: string
  assignedCategories?: string
}

const AddUserModal: React.FC<AddUserModalProps> = ({ isOpen, onClose }) => {
  const dispatch = useAppDispatch()
  const { isCreating } = useAppSelector((state: any) => state.users)
  const { categories, isLoading: categoriesLoading } = useAppSelector((state: any) => state.categories)

  const [formData, setFormData] = useState<CreateUserFormData>({
    name: '',
    email: '',
    password: '',
    userType: 'staff',
    assignedCategories: []
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [showPassword, setShowPassword] = useState(false)

  // Fetch categories when modal opens
  useEffect(() => {
    if (isOpen && categories.length === 0) {
      dispatch(fetchCategories())
    }
  }, [isOpen, categories.length, dispatch])

  // Get parent categories (categories without parentCategory)
  const parentCategories = categories.filter((cat: Category) => !cat.parentCategory)

  // Get subcategories for a specific parent category
  const getSubcategories = (parentId: string) => {
    return categories.filter((cat: Category) => cat.parentCategory === parentId)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear error when user types
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }

    // Clear assigned categories when user type changes from manager
    if (name === 'userType' && value !== 'manager') {
      setFormData(prev => ({
        ...prev,
        assignedCategories: []
      }))
    }
  }

  const handleCategoryToggle = (categoryId: string) => {
    setFormData(prev => {
      const currentCategories = prev.assignedCategories || []
      const isSelected = currentCategories.includes(categoryId)

      // If we're deselecting a parent category, also deselect all its subcategories
      if (isSelected) {
        const subcategories = getSubcategories(categoryId)
        const subcategoryIds = subcategories.map(sub => sub.id)
        const filteredCategories = currentCategories.filter(id =>
          id !== categoryId && !subcategoryIds.includes(id)
        )

        return {
          ...prev,
          assignedCategories: filteredCategories
        }
      } else {
        // If selecting a parent category, just add it
        return {
          ...prev,
          assignedCategories: [...currentCategories, categoryId]
        }
      }
    })

    // Clear category error when user selects categories
    if (errors.assignedCategories) {
      setErrors(prev => ({
        ...prev,
        assignedCategories: ''
      }))
    }
  }

  const handleSubcategoryToggle = (subcategoryId: string, parentId: string) => {
    setFormData(prev => {
      const currentCategories = prev.assignedCategories || []
      const isSelected = currentCategories.includes(subcategoryId)
      const isParentSelected = currentCategories.includes(parentId)

      if (isSelected) {
        // Deselecting subcategory
        return {
          ...prev,
          assignedCategories: currentCategories.filter(id => id !== subcategoryId)
        }
      } else {
        // Selecting subcategory - ensure parent is also selected
        const newCategories = [...currentCategories, subcategoryId]
        if (!isParentSelected) {
          newCategories.push(parentId)
        }

        return {
          ...prev,
          assignedCategories: newCategories
        }
      }
    })

    // Clear category error when user selects categories
    if (errors.assignedCategories) {
      setErrors(prev => ({
        ...prev,
        assignedCategories: ''
      }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required'
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    if (!formData.password.trim()) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters'
    }

    if (!formData.userType) {
      newErrors.userType = 'User type is required'
    }

    if (formData.userType === 'manager' && (!formData.assignedCategories || formData.assignedCategories.length === 0)) {
      newErrors.assignedCategories = 'Managers must be assigned to at least one category'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (validateForm()) {
      try {
        await dispatch(createUser(formData)).unwrap()
        toast.success('User created successfully')
        handleClose()
      } catch (error) {
        toast.error(typeof error === 'string' ? error : 'Failed to create user')
      }
    }
  }

  const handleClose = () => {
    setFormData({
      name: '',
      email: '',
      password: '',
      userType: 'staff',
      assignedCategories: []
    })
    setErrors({})
    setShowPassword(false)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-gray-100/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Add User</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Body */}
        <form onSubmit={handleSubmit} className="p-4 sm:p-6">
          {/* Name Field */}
          <div className="mb-4">
            <label htmlFor="name" className="flex text-sm font-medium text-gray-700 mb-2">
              Name *
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-lg   ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter user name"
                disabled={isCreating}
              />
            </div>
            {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
          </div>

          {/* Email Field */}
          <div className="mb-4">
            <label htmlFor="email" className="flex text-sm font-medium text-gray-700 mb-2">
              Email *
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2  ${
                  errors.email ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter email address"
                disabled={isCreating}
              />
            </div>
            {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
          </div>

          {/* Password Field */}
          <div className="mb-4">
            <label htmlFor="password" className="flex text-sm font-medium text-gray-700 mb-2">
              Password *
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                className={`w-full pl-10 pr-10 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#BE935E] ${
                  errors.password ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter password"
                disabled={isCreating}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                {showPassword ? '👁️' : '👁️‍🗨️'}
              </button>
            </div>
            {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
          </div>

          {/* User Type Field */}
          <div className="mb-4">
            <label htmlFor="userType" className="flex text-sm font-medium text-gray-700 mb-2">
              User Type *
            </label>
            <div className="relative">
              <UserCheck className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <select
                id="userType"
                name="userType"
                value={formData.userType}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#BE935E] ${
                  errors.userType ? 'border-red-500' : 'border-gray-300'
                }`}
                disabled={isCreating}
              >
                <option value="staff">Staff</option>
                <option value="manager">Manager</option>
                <option value="client">Client</option>
              </select>
            </div>
            {errors.userType && <p className="text-red-500 text-xs mt-1">{errors.userType}</p>}
          </div>

          {/* Category Assignment for Managers */}
          {formData.userType === 'manager' && (
            <div className="mb-6">
              <label className="flex text-sm font-medium text-gray-700 mb-2">
                Assigned Categories *
              </label>
              {categoriesLoading ? (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#BE935E]"></div>
                </div>
              ) : (
                <div className="space-y-3 max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-3">
                  {parentCategories.length === 0 ? (
                    <p className="text-gray-500 text-sm">No categories available</p>
                  ) : (
                    parentCategories.map((category: Category) => {
                      const subcategories = getSubcategories(category.id)
                      const isParentSelected = formData.assignedCategories?.includes(category.id) || false

                      return (
                        <div key={category.id} className="space-y-2">
                          {/* Parent Category */}
                          <label className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded">
                            <div className="relative">
                              <input
                                type="checkbox"
                                checked={isParentSelected}
                                onChange={() => handleCategoryToggle(category.id)}
                                className="sr-only"
                                disabled={isCreating}
                              />
                              <div className={`w-5 h-5 border-2 rounded flex items-center justify-center ${
                                isParentSelected
                                  ? 'bg-[#BE935E] border-[#BE935E]'
                                  : 'border-gray-300'
                              }`}>
                                {isParentSelected && (
                                  <Check className="w-3 h-3 text-white" />
                                )}
                              </div>
                            </div>
                            <span className="text-sm font-medium text-gray-700">{category.title}</span>
                            {subcategories.length > 0 && (
                              <span className="text-xs text-gray-500">({subcategories.length} subcategories)</span>
                            )}
                          </label>

                          {/* Subcategories - only show if parent is selected */}
                          {isParentSelected && subcategories.length > 0 && (
                            <div className="ml-8 space-y-1 border-l-2 border-gray-200 pl-3">
                              {subcategories.map((subcategory: Category) => (
                                <label
                                  key={subcategory.id}
                                  className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 p-1 rounded text-sm"
                                >
                                  <div className="relative">
                                    <input
                                      type="checkbox"
                                      checked={formData.assignedCategories?.includes(subcategory.id) || false}
                                      onChange={() => handleSubcategoryToggle(subcategory.id, category.id)}
                                      className="sr-only"
                                      disabled={isCreating}
                                    />
                                    <div className={`w-4 h-4 border-2 rounded flex items-center justify-center ${
                                      formData.assignedCategories?.includes(subcategory.id)
                                        ? 'bg-[#BE935E] border-[#BE935E]'
                                        : 'border-gray-300'
                                    }`}>
                                      {formData.assignedCategories?.includes(subcategory.id) && (
                                        <Check className="w-2.5 h-2.5 text-white" />
                                      )}
                                    </div>
                                  </div>
                                  <span className="text-sm text-gray-600">{subcategory.title}</span>
                                </label>
                              ))}
                            </div>
                          )}
                        </div>
                      )
                    })
                  )}
                </div>
              )}
              {errors.assignedCategories && (
                <p className="text-red-500 text-xs mt-1">{errors.assignedCategories}</p>
              )}
            </div>
          )}

          {/* Footer */}
          <div className="flex gap-3">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              disabled={isCreating}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isCreating}
              className="flex-1 bg-[#BE935E] text-white px-4 py-2 rounded-lg hover:bg-[#A67C52] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {isCreating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Creating...
                </>
              ) : (
                'Create User'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default AddUserModal
