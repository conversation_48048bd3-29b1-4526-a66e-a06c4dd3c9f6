import { X, FileText, User, Calendar, Tag, Paperclip, AlertTriangle, Download, Image } from 'lucide-react'
import type { Issue } from '../types/issue'
import type { User as UserType } from '../types/user'
import type { Category } from '../types/category'
import { formatDate } from '../utils/app.utils'

interface IssueDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  issue: Issue | null
  users: UserType[]
  categories: Category[]
}

const IssueDetailsModal = ({ isOpen, onClose, issue, users, categories }: IssueDetailsModalProps) => {
  // Helper function to get username from user ID
  const getUserName = (userId: string): string => {
    const user = users.find(u => u.id === userId)
    return user ? user.name : 'Unknown User'
  }

  // Helper function to get category title from category ID
  const getCategoryTitle = (categoryId: string): string => {
    const category = categories.find(c => c.id === categoryId)
    return category ? category.title : categoryId
  }

  // Helper function to check if file is an image
  const isImageFile = (url: string): boolean => {
    if (!url) return false
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
    const urlLower = url.toLowerCase()
    return imageExtensions.some(ext => urlLower.endsWith(ext) || urlLower.includes(ext + '?'))
  }

  // Helper function to get file extension
  const getFileExtension = (path: string): string => {
    return path.split('.').pop()?.toLowerCase() || ''
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'resolved':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'closed':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-red-100 text-red-600'
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-600'
      case 'resolved':
        return 'bg-green-100 text-green-600'
      case 'closed':
        return 'bg-gray-100 text-gray-600'
      default:
        return 'bg-gray-100 text-gray-600'
    }
  }

  if (!isOpen || !issue) return null

  return (
    <div className="fixed inset-0 bg-gray-100/50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-[#BE935E] px-4 sm:px-6 py-3 sm:py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2 sm:space-x-3">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-white rounded-full flex items-center justify-center">
              <FileText className="w-4 h-4 sm:w-6 sm:h-6 text-orange-500" />
            </div>
            <div>
              <h2 className="text-lg sm:text-xl text-left font-bold text-white">Issue Details</h2>
              <p className="text-orange-100 text-xs sm:text-sm hidden sm:block">View complete issue information</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-orange-100 hover:text-white cursor-pointer p-1.5 sm:p-2 rounded-lg transition-colors"
          >
            <X className="w-4 h-4 sm:w-5 sm:h-5" />
          </button>
        </div>

        {/* Body */}
        <div className="p-3 sm:p-6 overflow-y-auto max-h-[calc(95vh-80px)] sm:max-h-[calc(90vh-120px)]">
          {/* Issue Header */}
          <div className="flex flex-col sm:flex-row items-start space-y-3 sm:space-y-0 sm:space-x-4 mb-4 sm:mb-6 p-3 sm:p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-[#BE935E] rounded-full flex items-center justify-center shadow-lg">
              <FileText className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg sm:text-xl font-bold text-gray-900 text-left mb-2">Issue Report</h3>
              <div className="flex items-center space-x-2">
                <span className={`inline-flex px-2 sm:px-3 py-1 text-xs sm:text-sm font-semibold rounded-full border ${getStatusColor(issue.status)}`}>
                  {issue.status.charAt(0).toUpperCase() + issue.status.slice(1).replace('-', ' ')}
                </span>
              </div>
            </div>
          </div>

          {/* Issue Information Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-4 sm:mb-6">
            {/* Status */}
            <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4">
              <div className="flex items-center space-x-2 sm:space-x-3">
                <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-lg flex items-center justify-center ${getStatusIcon(issue.status)}`}>
                  <AlertTriangle className="w-4 h-4 sm:w-5 sm:h-5" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-gray-500 text-left">Status</p>
                  <p className="text-sm sm:text-sm font-semibold text-gray-900 truncate text-left">
                    {issue.status.charAt(0).toUpperCase() + issue.status.slice(1).replace('-', ' ')}
                  </p>
                </div>
              </div>
            </div>

            {/* User */}
            <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4">
              <div className="flex items-center space-x-2 sm:space-x-3">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <User className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-gray-500 text-left">Reported By</p>
                  <p className="text-sm sm:text-sm font-semibold text-gray-900 truncate text-left">{getUserName(issue.userId)}</p>
                </div>
              </div>
            </div>

            {/* Created Date */}
            <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4">
              <div className="flex items-center space-x-2 sm:space-x-3">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Calendar className="w-4 h-4 sm:w-5 sm:h-5 text-purple-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-gray-500 text-left">Date Created</p>
                  <p className="text-sm sm:text-sm font-semibold text-gray-900 truncate text-left">
                    {formatDate(issue.createdAt)}
                  </p>
                </div>
              </div>
            </div>

            {/* Updated Date */}
            <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4">
              <div className="flex items-center space-x-2 sm:space-x-3">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Calendar className="w-4 h-4 sm:w-5 sm:h-5 text-green-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-gray-500 text-left">Last Updated</p>
                  <p className="text-sm sm:text-sm font-semibold text-gray-900 truncate text-left">
                    {formatDate(issue.updatedAt)}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Issue Note */}
          <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6">
            <div className="flex items-start space-x-2 sm:space-x-3 mb-3">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <FileText className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600" />
              </div>
              <div className='text-left'>
                <h4 className="text-base sm:text-lg font-semibold text-gray-900">Issue Description</h4>
                <p className="text-xs sm:text-sm text-gray-500">Detailed description of the reported issue</p>
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
              <p className="text-sm sm:text-base text-gray-900 text-left ">{issue.note}</p>
            </div>
          </div>

          {/* Categories */}
          <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6">
            <div className="flex items-center space-x-2 sm:space-x-3 mb-3 sm:mb-4">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                <Tag className="w-4 h-4 sm:w-5 sm:h-5 text-indigo-600" />
              </div>
              <div>
                <h4 className="text-base sm:text-lg font-semibold text-gray-900">Categories</h4>
                <p className="text-xs sm:text-sm text-gray-500">Issue categories and tags</p>
              </div>
            </div>

            {issue.categories.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                {issue.categories.map((categoryId, index) => {
                  const categoryTitle = getCategoryTitle(categoryId)
                  const category = categories.find(c => c.id === categoryId)

                  return (
                    <div
                      key={index}
                      className="flex items-center space-x-2 p-2 bg-indigo-50 rounded-lg border border-indigo-100"
                    >
                      {category?.icon?.url ? (
                        <img
                          src={category.icon.url}
                          alt={categoryTitle}
                          className="w-5 h-5 rounded object-cover flex-shrink-0"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            target.nextElementSibling?.classList.remove('hidden');
                          }}
                        />
                      ) : null}
                      <Tag className={`w-4 h-4 text-indigo-600 flex-shrink-0 ${category?.icon?.url ? 'hidden' : ''}`} />
                      <span className="text-sm font-medium text-indigo-800 truncate">
                        {categoryTitle}
                      </span>
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="text-center py-4">
                <Tag className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">No categories assigned</p>
              </div>
            )}
          </div>

          {/* Attachments */}
          <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4">
            <div className="flex items-center space-x-2 sm:space-x-3 mb-3 sm:mb-4">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <Paperclip className="w-4 h-4 sm:w-5 sm:h-5 text-green-600" />
              </div>
              <div>
                <h4 className="text-base sm:text-lg font-semibold text-gray-900 text-left">Attachments</h4>
                <p className="text-xs sm:text-sm text-gray-500">Files and documents related to this issue</p>
              </div>
            </div>

            {issue.attachment.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {issue.attachment.map((attachment, index) => {
                  const isImage = isImageFile(attachment.url || attachment.path)
                  const fileExtension = getFileExtension(attachment.path)

                  return (
                    <div key={index} className="bg-gray-50 rounded-lg border border-gray-100 overflow-hidden">
                      {/* Image Display */}
                      { attachment.url ? (
                        <div className="relative group">
                          <img
                            src={attachment.url}
                            alt={attachment.path}
                            className="w-full h-48 object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              // Show fallback content
                              const fallback = target.parentElement?.nextElementSibling as HTMLElement;
                              if (fallback) {
                                fallback.style.display = 'block';
                              }
                            }}
                          />
                          {/* <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                            <a
                              href={attachment.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="bg-white bg-opacity-90 text-gray-800 px-3 py-1 rounded-lg text-sm font-medium hover:bg-opacity-100 transition-all"
                            >
                              View Full Size
                            </a>
                          </div> */}
                          {/* Image info overlay */}
                          {/* <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3">
                            <p className="text-white text-xs font-medium truncate">{attachment.path}</p>
                          </div> */}
                        </div>
                      ) : (
                        /* Non-image file display */
                        <div className="p-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                              <Paperclip className="w-6 h-6 text-blue-600" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 truncate">{attachment.path}</p>
                              <p className="text-xs text-gray-500 uppercase">{fileExtension} file</p>
                            </div>
                            {attachment.url && (
                              <a
                                href={attachment.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 text-sm font-medium"
                              >
                               
                              </a>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Fallback content for failed image loads (hidden by default) */}
                      {isImage && attachment.url && (
                        <div className="p-4 hidden">
                          <div className="flex items-center space-x-3">
                            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center flex-shrink-0">
                              <Image className="w-6 h-6 text-red-600" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 truncate">{attachment.path}</p>
                              <p className="text-xs text-red-500">Image failed to load</p>
                            </div>
                            <a
                              href={attachment.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 text-sm font-medium"
                            >
                              <Download className="w-4 h-4" />
                              <span>Download</span>
                            </a>
                          </div>
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="text-center py-8">
                <Paperclip className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <p className="text-sm text-gray-500">No attachments for this issue</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default IssueDetailsModal
