import { useEffect, useState } from 'react'
import IssueTable from '../components/IssueTable'
import { Search } from 'lucide-react'
import { toast } from 'sonner'
import type { Issue } from '../types/issue'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { fetchAllIssues } from '../store/slices/issuesSlice'
import { fetchAllUsers } from '../store/slices/usersSlice'
import { fetchCategories } from '../store/slices/categoriesSlice'

const Issues = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const dispatch = useAppDispatch()
  const { issues, isLoading, error } = useAppSelector((state:any) => state.issues)
  const { users } = useAppSelector((state:any) => state.users)
  const { categories } = useAppSelector((state:any) => state.categories)

  useEffect(() => {
    dispatch(fetchAllIssues())
    dispatch(fetchAllUsers())
    dispatch(fetchCategories())
  }, [dispatch])

  useEffect(() => {
    if (error) {
      toast.error(error)
    }
  }, [error])

  // Helper function to get username from user ID
  const getUserName = (userId: string): string => {
    const user = users.find((u:any) => u.id === userId)
    return user ? user.name : userId
  }

  const filteredIssues = issues.filter((issue: Issue) => {
    const userName = getUserName(issue.userId)
    const matchesSearch = searchTerm === '' ||
      issue.note.toLowerCase().includes(searchTerm.toLowerCase()) ||
      issue.userId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      issue.categories.some(cat => cat.toLowerCase().includes(searchTerm.toLowerCase()))

    return matchesSearch
  })

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Issues Management</h1>
            <p className="text-gray-600">Track and manage all reported issues</p>
          </div>
          <div className="flex items-center space-x-3">
            {/* <div className="bg-white rounded-lg shadow-sm border border-gray-200 px-4 py-2"> */}
              {/* <div className="flex items-center space-x-2">
                <AlertTriangle className="w-5 h-5 text-orange-500" />
                <span className="text-sm font-medium text-gray-700">
                  {filteredIssues.length} issue{filteredIssues.length !== 1 ? 's' : ''}
                </span>
              </div> */}
            {/* </div> */}
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search issues by note, username, user ID, or categories..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
          />
        </div>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
        </div>
      )}

      {/* Issues Table */}
      {!isLoading && (
        <IssueTable issues={filteredIssues} users={users} categories={categories} />
      )}

      {/* Empty state when no issues match filters */}
      {!isLoading && issues.length > 0 && filteredIssues.length === 0 && (
        <div className="text-center py-12 bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
            <Search className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-3">
            No issues match your filters
          </h3>
          <p className="text-gray-600 max-w-md mx-auto mb-4">
            Try adjusting your search terms to find what you're looking for.
          </p>
          <button
            onClick={() => {
              setSearchTerm('')
            }}
            className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
          >
            Clear Search
          </button>
        </div>
      )}
    </div>
  )
}

export default Issues

